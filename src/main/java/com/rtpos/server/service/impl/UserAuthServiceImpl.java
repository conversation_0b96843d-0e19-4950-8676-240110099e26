package com.rtpos.server.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rtpos.server.dto.auth.*;
import com.rtpos.server.service.UserAuthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * 用户认证服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserAuthServiceImpl implements UserAuthService {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Value("${auth.api.send-code-url}")
    private String sendCodeUrl;

    @Value("${auth.api.login-url}")
    private String loginUrl;

    @Value("${auth.api.query-emp-url}")
    private String queryEmpUrl;

    @Override
    public AuthResponse<Object> sendCode(SendCodeRequest request) {
        log.info("Sending verification code to mobile: {}", request.getMobile());

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(MediaType.APPLICATION_FORM_URLENCODED_VALUE));

            // 构建JSON数据
            String jsonData = String.format("{\"mobile\":\"%s\",\"app\":\"%s\"}",
                    request.getMobile(), request.getApp());

            MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
            formData.add("data", jsonData);

            HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(formData, headers);

            ResponseEntity<AuthResponse> response = restTemplate.exchange(
                    sendCodeUrl, HttpMethod.POST, entity, 
                    new ParameterizedTypeReference<AuthResponse>() {}
            );

            log.info("Send code response: {}", response.getBody());
            return response.getBody();

        } catch (Exception e) {
            log.error("Failed to send verification code", e);
            AuthResponse<Object> errorResponse = new AuthResponse<>();
            errorResponse.setCode(500);
            errorResponse.setMsg("发送验证码失败: " + e.getMessage());
            return errorResponse;
        }
    }

    @Override
    public AuthResponse<LoginData> login(LoginRequest request) {
        log.info("User login with mobile: {}", request.getMobile());

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(MediaType.APPLICATION_FORM_URLENCODED_VALUE));

            // 构建JSON数据
            String jsonData = String.format("{\"mobile\":\"%s\",\"app\":\"%s\",\"code\":\"%s\",\"type\":%d}",
                    request.getMobile(), request.getApp(), request.getCode(), request.getType());

            MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
            formData.add("data", jsonData);

            HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(formData, headers);

            ResponseEntity<AuthResponse<LoginData>> response = restTemplate.exchange(
                    loginUrl, HttpMethod.POST, entity,
                    new ParameterizedTypeReference<AuthResponse<LoginData>>() {}
            );

            log.info("Login response: {}", response.getBody());
            return response.getBody();

        } catch (Exception e) {
            log.error("Failed to login", e);
            AuthResponse<LoginData> errorResponse = new AuthResponse<>();
            errorResponse.setCode(500);
            errorResponse.setMsg("登录失败: " + e.getMessage());
            return errorResponse;
        }
    }

    @Override
    public AuthResponse<List<EmpDeptRole>> queryEmpDeptRole(QueryEmpRequest request) {
        log.info("Querying employee dept role for empId: {}", request.getEmpId());

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(MediaType.APPLICATION_FORM_URLENCODED_VALUE));

            // 构建JSON数据
            String jsonData = String.format("{\"emp_id\":\"%s\"}", request.getEmpId());

            MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
            formData.add("data", jsonData);

            HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(formData, headers);

            ResponseEntity<AuthResponse> response = restTemplate.exchange(
                    queryEmpUrl, HttpMethod.POST, entity, 
                    new ParameterizedTypeReference<AuthResponse>() {}
            );

            log.info("Query emp dept role response: {}", response.getBody());
            return response.getBody();

        } catch (Exception e) {
            log.error("Failed to query employee dept role", e);
            AuthResponse<List<EmpDeptRole>> errorResponse = new AuthResponse<>();
            errorResponse.setCode(500);
            errorResponse.setMsg("查询员工信息失败: " + e.getMessage());
            return errorResponse;
        }
    }
}
